'use client';

import { motion } from 'framer-motion';
import { Lightbulb, Users, Award, Linkedin, Github, Twitter } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

const About = () => {
  const pillars = [
    {
      icon: Lightbulb,
      title: 'Innovation',
      description: 'We constantly explore cutting-edge technologies to create forward-thinking solutions that push the boundaries of financial automation.',
    },
    {
      icon: Users,
      title: 'Collaboration',
      description: 'We work closely with clients to understand their unique needs and challenges, ensuring our solutions deliver maximum value.',
    },
    {
      icon: Award,
      title: 'Excellence',
      description: 'We are committed to delivering the highest quality solutions with measurable results and exceptional client satisfaction.',
    },
  ];

  const team = [
    {
      name: '<PERSON>',
      role: 'Co-Founder & CEO',
      bio: 'Visionary leader with 15+ years in financial technology and automation. Expert in scaling fintech solutions and driving digital transformation.',
      image: '/api/placeholder/300/300',
      social: {
        linkedin: '#',
        twitter: '#',
        github: '#'
      }
    },
    {
      name: '<PERSON><PERSON>',
      role: 'Co-Founder & CTO',
      bio: 'Technical architect specializing in AI/ML and financial systems. Passionate about building scalable automation platforms.',
      image: '/api/placeholder/300/300',
      social: {
        linkedin: '#',
        twitter: '#',
        github: '#'
      }
    },
    {
      name: 'Sarthak Rana',
      role: 'Co-Founder & COO',
      bio: 'Operations expert focused on process optimization and client success. Drives operational excellence and strategic partnerships.',
      image: '/api/placeholder/300/300',
      social: {
        linkedin: '#',
        twitter: '#',
        github: '#'
      }
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: 'easeOut' },
    },
  };

  return (
    <section id="about" className="py-24 bg-background">
      <div className="container-custom">
        {/* Mission Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-20"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
            About <span className="text-ocean-gradient">MizuFlow</span>
          </h2>
          <div className="max-w-4xl mx-auto space-y-6">
            <p className="text-lg md:text-xl text-muted-foreground leading-relaxed">
              MizuFlow is a trusted provider of financial automation and business intelligence solutions. 
              We combine cutting-edge technology with deep financial expertise to help businesses streamline 
              operations, improve accuracy, and make data-driven decisions.
            </p>
            <p className="text-lg text-muted-foreground leading-relaxed">
              Our team of financial experts and technology specialists work together to deliver tailored 
              solutions that address your unique business challenges and drive sustainable growth.
            </p>
          </div>
        </motion.div>

        {/* Three Pillars */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="mb-24"
        >
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">Our Core Values</h3>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              The principles that guide everything we do and drive our commitment to excellence.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {pillars.map((pillar, index) => (
              <motion.div key={pillar.title} variants={itemVariants}>
                <Card className="h-full text-center group hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/30">
                  <CardContent className="p-8">
                    <div className="space-y-6">
                      <div className="relative mx-auto w-20 h-20">
                        <div className="w-20 h-20 bg-ocean-gradient-light rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                          <pillar.icon className="w-10 h-10 text-primary" />
                        </div>
                        <div className="absolute inset-0 bg-primary/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>
                      <h4 className="text-xl font-bold text-foreground group-hover:text-primary transition-colors">
                        {pillar.title}
                      </h4>
                      <p className="text-muted-foreground leading-relaxed">
                        {pillar.description}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Team Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">Meet Our Team</h3>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              The passionate experts behind MizuFlow's innovative financial automation solutions.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <motion.div key={member.name} variants={itemVariants}>
                <Card className="group hover:shadow-xl transition-all duration-300 border-border/50 hover:border-primary/30 overflow-hidden">
                  <CardContent className="p-0">
                    <div className="relative">
                      {/* Profile Image Placeholder */}
                      <div className="aspect-square bg-ocean-gradient-light flex items-center justify-center">
                        <div className="w-24 h-24 bg-primary/20 rounded-full flex items-center justify-center">
                          <Users className="w-12 h-12 text-primary" />
                        </div>
                      </div>
                      
                      {/* Social Links Overlay */}
                      <div className="absolute inset-0 bg-primary/90 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <div className="flex space-x-4">
                          <a
                            href={member.social.linkedin}
                            className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                            aria-label={`${member.name} LinkedIn`}
                          >
                            <Linkedin className="w-5 h-5 text-white" />
                          </a>
                          <a
                            href={member.social.twitter}
                            className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                            aria-label={`${member.name} Twitter`}
                          >
                            <Twitter className="w-5 h-5 text-white" />
                          </a>
                          <a
                            href={member.social.github}
                            className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                            aria-label={`${member.name} GitHub`}
                          >
                            <Github className="w-5 h-5 text-white" />
                          </a>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-6 space-y-4">
                      <div className="text-center">
                        <h4 className="text-xl font-bold text-foreground">{member.name}</h4>
                        <p className="text-primary font-medium">{member.role}</p>
                      </div>
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {member.bio}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
