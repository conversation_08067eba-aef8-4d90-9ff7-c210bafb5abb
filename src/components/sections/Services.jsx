'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FileText,
  Receipt,
  Calculator,
  TrendingUp,
  Brain,
  BarChart3,
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const Services = () => {
  const [activeService, setActiveService] = useState(0);

  const services = [
    {
      category: 'Automated Financial Process',
      icon: FileText,
      title: 'Invoice Automation',
      description: 'Automate email monitoring and SharePoint integration, extract invoice data using Azure Form Recognizer, and streamline accounting system entries with human validation.',
      features: [
        'Email monitoring & SharePoint integration',
        'Azure Form Recognizer data extraction',
        'Automated accounting system entries',
        'Human validation workflows',
        'Real-time processing status'
      ],
      benefits: ['95% time reduction', '99.9% accuracy', 'Seamless integration']
    },
    {
      category: 'Automated Financial Process',
      icon: Receipt,
      title: 'Sales Invoice Processing',
      description: 'Automate invoice generation, implement rule-based validation, and integrate with accounting systems using RPA tools.',
      features: [
        'Automated invoice generation',
        'Rule-based validation engine',
        'RPA tool integration',
        'Multi-system connectivity',
        'Exception handling'
      ],
      benefits: ['Instant processing', 'Error elimination', 'Cost reduction']
    },
    {
      category: 'Automated Financial Process',
      icon: Calculator,
      title: 'Account Reconciliation',
      description: 'Automate reconciliation using Excel, Python, and AI-driven matching for large transactions.',
      features: [
        'Excel & Python automation',
        'AI-driven transaction matching',
        'Large volume processing',
        'Discrepancy identification',
        'Automated reporting'
      ],
      benefits: ['100% accuracy', 'Real-time results', 'Audit trail']
    },
    {
      category: 'FP&A Functions',
      icon: TrendingUp,
      title: 'Cash Flow Forecasting',
      description: 'Develop daily/weekly cash flow templates in Excel to enhance real-time forecasting.',
      features: [
        'Daily/weekly templates',
        'Real-time data integration',
        'Scenario modeling',
        'Variance analysis',
        'Executive dashboards'
      ],
      benefits: ['Improved liquidity', 'Better planning', 'Risk mitigation']
    },
    {
      category: 'FP&A Functions',
      icon: Brain,
      title: 'AI-Driven Budgeting & Forecasting',
      description: 'Leverage machine learning and deep learning models to enhance forecasting accuracy and automate budget planning.',
      features: [
        'Machine learning models',
        'Deep learning algorithms',
        'Automated budget planning',
        'Predictive analytics',
        'Continuous model improvement'
      ],
      benefits: ['Higher accuracy', 'Automated insights', 'Strategic planning']
    },
    {
      category: 'Financial Reporting & BI',
      icon: BarChart3,
      title: 'Financial Statement & MD&A Automation',
      description: 'Integrate data from accounting systems to generate automated reports using Power BI and Power Automate.',
      features: [
        'Power BI integration',
        'Power Automate workflows',
        'Automated report generation',
        'Data visualization',
        'Compliance reporting'
      ],
      benefits: ['Time savings', 'Consistent reporting', 'Real-time insights']
    }
  ];

  return (
    <section id="services" className="py-24 bg-ocean-gradient-light">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
            Our <span className="text-ocean-gradient">Services</span>
          </h2>
          <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Comprehensive financial automation and business intelligence solutions tailored to your needs.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Service Timeline */}
          <div className="space-y-4">
            {services.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`relative cursor-pointer transition-all duration-300 ${
                  activeService === index ? 'scale-105' : 'hover:scale-102'
                }`}
                onClick={() => setActiveService(index)}
              >
                <Card className={`border-2 transition-all duration-300 ${
                  activeService === index
                    ? 'border-primary shadow-lg bg-white'
                    : 'border-border/50 hover:border-primary/30 bg-white/80'
                }`}>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 ${
                        activeService === index
                          ? 'bg-ocean-gradient text-white'
                          : 'bg-muted text-muted-foreground'
                      }`}>
                        <service.icon className="w-6 h-6" />
                      </div>
                      <div className="flex-1">
                        <div className="text-sm text-primary font-medium mb-1">
                          {service.category}
                        </div>
                        <h3 className="font-bold text-foreground">
                          {service.title}
                        </h3>
                      </div>
                      {activeService === index && (
                        <CheckCircle className="w-6 h-6 text-primary" />
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Service Details */}
          <div className="lg:sticky lg:top-24">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeService}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.4 }}
              >
                <Card className="bg-white shadow-xl border-border/20">
                  <CardContent className="p-8">
                    <div className="space-y-6">
                      {/* Header */}
                      <div className="flex items-center space-x-4">
                        <div className="w-16 h-16 bg-ocean-gradient rounded-2xl flex items-center justify-center">
                          {React.createElement(services[activeService].icon, {
                            className: "w-8 h-8 text-white"
                          })}
                        </div>
                        <div>
                          <div className="text-sm text-primary font-medium">
                            {services[activeService].category}
                          </div>
                          <h3 className="text-2xl font-bold text-foreground">
                            {services[activeService].title}
                          </h3>
                        </div>
                      </div>

                      {/* Description */}
                      <p className="text-muted-foreground leading-relaxed">
                        {services[activeService].description}
                      </p>

                      {/* Features */}
                      <div>
                        <h4 className="font-semibold text-foreground mb-3">Key Features:</h4>
                        <div className="space-y-2">
                          {services[activeService].features.map((feature, index) => (
                            <div key={index} className="flex items-center space-x-3">
                              <div className="w-2 h-2 bg-primary rounded-full"></div>
                              <span className="text-sm text-muted-foreground">{feature}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Benefits */}
                      <div>
                        <h4 className="font-semibold text-foreground mb-3">Benefits:</h4>
                        <div className="flex flex-wrap gap-2">
                          {services[activeService].benefits.map((benefit, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 bg-primary/10 text-primary text-sm rounded-full border border-primary/20"
                            >
                              {benefit}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* CTA */}
                      <Button className="w-full bg-ocean-gradient hover:opacity-90 text-white group">
                        Learn More About This Service
                        <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
